import { useEffect, useState, useCallback, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Select,
  Tooltip,
  theme as antdTheme,
  Flex,
} from "antd";
import G6 from "@antv/g6";
import domtoimage from "dom-to-image";
import saveAs from "file-saver";
import { useDispatch, useSelector } from "react-redux";
import {
  FileAddOutlined,
  FileSyncOutlined,
  PlusOutlined,
  ReloadOutlined,
} from "@ant-design/icons";

// Redux imports
import {
  getGraphDataOnClientChange,
  getTopologyData,
  newTopologySelector,
  saveNodesPosition,
  setGPhysics,
  setIsEditMode,
  cleanupOldTopologyData,
} from "../../features/topology/topologySlice";

// Component imports
import { TopologyImage } from "../../components/topology/TopologyImage";
import graphMemoryOptimizer from "../../utils/graphMemoryOptimizer";

// Utility function to safely call fitView
const safeFitView = (graph, padding = 5) => {
  if (!graph || graph.destroyed || typeof graph.fitView !== "function") {
    return false;
  }

  try {
    // Check if the graph has valid internal state
    if (graph.get && typeof graph.get === "function") {
      try {
        const viewController = graph.get("viewController");
        if (viewController && !viewController.destroyed) {
          graph.fitView(padding);
          return true;
        }
      } catch (viewControllerError) {
        console.warn(
          "ViewController check failed, skipping fitView:",
          viewControllerError
        );
        return false;
      }
    } else {
      // Fallback: try fitView without checks
      graph.fitView(padding);
      return true;
    }
  } catch (error) {
    console.warn("Error in safeFitView:", error);
    return false;
  }
  return false;
};
import { getFilename } from "../../components/exportData/ExportData";
import ManualTopoForm from "../../components/topology/ManualTopoFormModel";
import ManualTopoSaveRestoreForm from "../../components/topology/ManualTopoSaveRestoreForm";

// API imports
import {
  useAddTopologyMutation,
  useSaveRestoreTopologyMutation,
} from "../../app/services/commandApi";

// Utility imports
import { createManualTpologyData } from "../../utils/comman/dataMapping";

// Constants
const ANIMATION_CONFIG = { duration: 200, easing: "easeCubic" };
const GRAPH_CONFIG = {
  DEFAULT_NODE_SIZE: [56, 56],
  FORCE_LAYOUT: {
    linkDistance: 250,
    nodeStrength: -50,
    edgeStrength: 0.1,
    collideStrength: 0.8,
    nodeSize: 30,
    alpha: 0.3,
    alphaDecay: 0.028,
    alphaMin: 0.01,
  },
  ANIMATION: {
    repeat: true,
    duration: 3000,
  },
};

// G6 Edge Registration
G6.registerEdge(
  "circle-running",
  {
    afterDraw(cfg, group) {
      const shape = group.get("children")[0];
      const startPoint = shape.getPoint(0);
      const color = cfg.circleColor || "#1890ff";

      if (!startPoint || color === "transparent") return;

      // Remove any existing circles to prevent duplicates
      const existingCircles = group.findAll(
        (element) => element.get("name") === "running-circle"
      );
      existingCircles.forEach((circle) => circle.remove());

      const circle = group.addShape("circle", {
        attrs: {
          x: startPoint.x,
          y: startPoint.y,
          fill: color,
          r: 3,
        },
        name: "running-circle", // Add name for identification
      });

      circle.animate((ratio) => {
        const tmpPoint = shape.getPoint(ratio);
        return tmpPoint ? { x: tmpPoint.x, y: tmpPoint.y } : { x: 0, y: 0 };
      }, GRAPH_CONFIG.ANIMATION);
    },
    afterUpdate(cfg, item) {
      // Re-trigger afterDraw when edge is updated
      const group = item.getContainer();
      this.afterDraw(cfg, group);
    },
    setState(name, value, item) {
      // Handle state changes that might affect animation
      if (name === "refresh" && value) {
        const group = item.getContainer();
        const cfg = item.getModel();
        this.afterDraw(cfg, group);
      }
    },
  },
  "line"
);

// Toolbar Configuration
const createToolbar = () =>
  new G6.ToolBar({
    position: { x: 10, y: 10 },
    getContent: () => `
    <ul class='g6-component-toolbar'>
      <li code='zoomOut'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M658.432 428.736a33.216 33.216 0 0 1-33.152 33.152H525.824v99.456a33.216 33.216 0 0 1-66.304 0V461.888H360.064a33.152 33.152 0 0 1 0-66.304H459.52V296.128a33.152 33.152 0 0 1 66.304 0V395.52H625.28c18.24 0 33.152 14.848 33.152 33.152z m299.776 521.792a43.328 43.328 0 0 1-60.864-6.912l-189.248-220.992a362.368 362.368 0 0 1-215.36 70.848 364.8 364.8 0 1 1 364.8-364.736 363.072 363.072 0 0 1-86.912 235.968l192.384 224.64a43.392 43.392 0 0 1-4.8 61.184z m-465.536-223.36a298.816 298.816 0 0 0 298.432-298.432 298.816 298.816 0 0 0-298.432-298.432A298.816 298.816 0 0 0 194.24 428.8a298.816 298.816 0 0 0 298.432 298.432z"></path>
        </svg>
      </li>
      <li code='zoomIn'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="24" height="24">
          <path d="M639.936 416a32 32 0 0 1-32 32h-256a32 32 0 0 1 0-64h256a32 32 0 0 1 32 32z m289.28 503.552a41.792 41.792 0 0 1-58.752-6.656l-182.656-213.248A349.76 349.76 0 0 1 480 768 352 352 0 1 1 832 416a350.4 350.4 0 0 1-83.84 227.712l185.664 216.768a41.856 41.856 0 0 1-4.608 59.072zM479.936 704c158.784 0 288-129.216 288-288S638.72 128 479.936 128a288.32 288.32 0 0 0-288 288c0 158.784 129.216 288 288 288z" p-id="3853"></path>
        </svg>
      </li>
      <li code='realZoom'>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="24">
          <path d="M384 320v384H320V320h64z m256 0v384H576V320h64zM512 576v64H448V576h64z m0-192v64H448V384h64z m355.968 576H92.032A28.16 28.16 0 0 1 64 931.968V28.032C64 12.608 76.608 0 95.168 0h610.368L896 192v739.968a28.16 28.16 0 0 1-28.032 28.032zM704 64v128h128l-128-128z m128 192h-190.464V64H128v832h704V256z"></path>
        </svg>
      </li>
    </ul>`,
    handleClick: (code, graph) => {
      switch (code) {
        case "zoomOut":
          graph.zoom(1.2, undefined, true, ANIMATION_CONFIG);
          break;
        case "zoomIn":
          graph.zoom(0.8, undefined, true, ANIMATION_CONFIG);
          break;
        case "realZoom":
          graph.zoomTo(1, undefined, true, ANIMATION_CONFIG);
          break;
        case "autoZoom":
          safeFitView(graph, 5);
          break;
        default:
          break;
      }
    },
  });

// Utility Functions
const createDataSaveNodePosition = (data = []) => {
  return data.map((node) => ({
    id: node.id,
    x: node.x,
    y: node.y,
  }));
};

const TopologyPage = () => {
  // Hooks
  const { token } = antdTheme.useToken();
  const { notification } = App.useApp();
  const dispatch = useDispatch();

  // Local State
  const [isRendered, setIsRendered] = useState(false);
  const [openAddtopoForm, setOpenAddtopoForm] = useState(false);
  const [openSaveRestore, setOpenSaveRestore] = useState(false);
  const [actionType, setActionType] = useState("");
  const [tGraph, setTGraph] = useState(null);

  // Refs for tracking data changes
  const lastDataRef = useRef(null);
  const forceUpdateRef = useRef(0);

  // API Mutations
  const [addTopology, { isLoading: topoLoading }] = useAddTopologyMutation();
  const [saveRestoreTopology, { isLoading: saveResLoading }] =
    useSaveRestoreTopologyMutation();

  // Redux Selectors
  const { isEditMode, gPhysics, gData, savedNodes, clientsData, reqClient } =
    useSelector(newTopologySelector);

  console.log("gData:", gData);

  // Debug: Log when gData changes
  useEffect(() => {
    console.log("gData changed, new data:", gData);
    console.log(
      "gData edges:",
      gData?.edges?.map((edge) => ({
        id: edge.id,
        linkType: edge.linkType,
        style: edge.style,
      }))
    );
  }, [gData]);

  //Re-render the graph on theme update
  useEffect(() => {
    setIsRendered(false);
  }, [token]);

  // Memoized Functions
  const getNodePosition = useCallback(
    (nodeId) => {
      const savedNode = savedNodes?.find((node) => node.id === nodeId);
      return savedNode && (savedNode.x !== null || savedNode.y !== null)
        ? { x: savedNode.x, y: savedNode.y }
        : { x: undefined, y: undefined };
    },
    [savedNodes]
  );

  const createLabelStyle = useCallback(
    (tokenColor) => ({
      style: {
        fill: tokenColor,
        fontSize: 18,
        background: {
          fill: "transparent",
          padding: [2, 2, 2, 2],
        },
      },
    }),
    []
  );

  const createData = useCallback(
    (data) => {
      if (!data?.nodes || !data?.edges) return { nodes: [], edges: [] };

      const labelStyle = createLabelStyle(token.colorText);

      const nodes = data.nodes.map((node) => {
        const position = isEditMode
          ? { x: undefined, y: undefined }
          : getNodePosition(node.id);
        return {
          ...node,
          ...position,
          label: `${node.ipAddress}\n${node.macAddress}\n${node.modelname}`,
          img: TopologyImage(node.modelname),
          labelCfg: {
            ...labelStyle,
            position: "bottom",
          },
        };
      });

      const edges = data.edges.map((edge) => ({
        ...edge,
        label: `${edge.source}_${edge.sourcePort}\n${edge.target}_${edge.targetPort}`,
        color:
          edge.blockedPort === "true"
            ? "#faad14"
            : edge.linkType === "manual"
            ? "#722ed1"
            : token.colorTextDisabled,
        circleColor:
          edge.blockedPort === "true" ||
          edge.linkType === "dashed" ||
          edge.linkType === "manual"
            ? "transparent"
            : token.colorPrimary,
        labelCfg: labelStyle,
        style: {
          lineWidth: 2,
          lineDash:
            edge.linkType === "dashed" || edge.linkType === "manual"
              ? [4, 4]
              : undefined,
        },
      }));

      return { nodes, edges };
    },
    [token, isEditMode, getNodePosition, createLabelStyle]
  );

  // Event Handlers
  const handleEditModeChange = useCallback(
    (e) => {
      dispatch(setIsEditMode(e.target.checked));
    },
    [dispatch]
  );

  const handleSaveNodePosition = useCallback(() => {
    if (!tGraph || tGraph.destroyed) return;

    const { nodes } = tGraph.save();
    dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
    dispatch(setIsEditMode(false));
  }, [tGraph, dispatch]);

  const handlePhysicsChange = useCallback(
    (e) => {
      dispatch(setGPhysics(e.target.checked));
    },
    [dispatch]
  );

  const handleFitView = useCallback(() => {
    if (!tGraph || tGraph.destroyed) return;

    if (safeFitView(tGraph, 5)) {
      const { nodes } = tGraph.save();
      dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
    }
  }, [tGraph, dispatch]);

  const handleRefresh = useCallback(() => {
    dispatch(getTopologyData());
    setIsRendered(false);

    // Force refresh animations after data reload
    if (tGraph && !tGraph.destroyed) {
      setTimeout(() => {
        tGraph.getEdges().forEach((edge) => {
          edge.setState("refresh", true);
          edge.setState("refresh", false);
        });
      }, 200);
    }
  }, [dispatch, tGraph]);

  const handleDownloadImage = useCallback(() => {
    const container = document.getElementById("topology-container");
    if (!container) return;

    const filter = (node) => node.tagName !== "i";

    domtoimage
      .toSvg(container, { filter })
      .then((dataUrl) => {
        saveAs(dataUrl, `${getFilename("topology")}.svg`);
      })
      .catch((error) => {
        notification.error({
          message: "Export failed",
          description: error.message,
        });
      });
  }, [notification]);

  // Graph Initialization
  useEffect(() => {
    if (!tGraph) {
      const container = document.getElementById("topology-container");
      if (!container) return;

      const width = container.clientWidth;
      const height = container.clientHeight || 500;

      const graph = new G6.Graph({
        container: "topology-container",
        width,
        height,
        plugins: [createToolbar()],
        renderer: "svg",
        linkCenter: true,
        animate: true,
        modes: {
          default: ["zoom-canvas", "drag-canvas", "click-select", "drag-node"],
        },
        defaultNode: {
          type: "image",
          size: GRAPH_CONFIG.DEFAULT_NODE_SIZE,
        },
        defaultEdge: {
          type: "circle-running",
        },
      });

      setTGraph(graph);

      // Register with memory optimizer
      graphMemoryOptimizer.registerGraph(graph, "TopologyPage-Main");
    }
  }, [tGraph]);

  // Window Resize Handler
  useEffect(() => {
    const handleResize = () => {
      if (!tGraph || tGraph.get("destroyed")) return;

      const container = document.getElementById("topology-container");
      if (!container?.clientWidth || !container?.clientHeight) return;

      tGraph.changeSize(container.clientWidth, container.clientHeight);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [tGraph]);

  // Physics Layout Effect
  useEffect(() => {
    if (!tGraph || tGraph.destroyed || !gPhysics) return;

    tGraph.updateLayout({
      type: "force",
      ...GRAPH_CONFIG.FORCE_LAYOUT,
      forceSimulation: null,
      onTick: () => {
        // Optional: Add tick logging if needed for debugging
        // console.log("Physics simulation ticking");
      },
      onLayoutEnd: () => {
        const { nodes } = tGraph.save();
        dispatch(saveNodesPosition(createDataSaveNodePosition(nodes)));
        dispatch(setGPhysics(false));
      },
    });
  }, [gPhysics, tGraph, dispatch]);

  // Data Rendering Effects
  useEffect(() => {
    if (tGraph && !isRendered && gData.nodes.length > 0) {
      tGraph.data(createData(gData));
      tGraph.render();

      // Force refresh edge animations after render
      setTimeout(() => {
        tGraph.getEdges().forEach((edge) => {
          edge.setState("refresh", true);
          edge.setState("refresh", false);
        });
      }, 100);

      setIsRendered(true);
    }

    return () => {
      if (tGraph && !tGraph.destroyed) {
        tGraph.destroyLayout();
        tGraph.changeData(createData(gData));
      }
    };
  }, [tGraph, isRendered, gData, createData]);

  // Data Update Effect
  useEffect(() => {
    if (!tGraph || tGraph.destroyed || !isRendered) return;

    // Check if data has actually changed by comparing JSON strings
    const currentDataString = JSON.stringify(gData);
    const lastDataString = lastDataRef.current;

    if (currentDataString === lastDataString) {
      return; // No change in data
    }

    console.log("Updating topology graph with new data:", gData);
    lastDataRef.current = currentDataString;
    forceUpdateRef.current += 1;

    try {
      const processedData = createData(gData);

      // Force a complete data refresh instead of just changeData
      tGraph.clear();
      tGraph.data(processedData);
      tGraph.render();

      // Force refresh edge animations after data change with cleanup
      const animationTimeoutId = setTimeout(() => {
        if (!tGraph.destroyed) {
          try {
            tGraph.getEdges().forEach((edge) => {
              edge.setState("refresh", true);
              edge.setState("refresh", false);
            });
            console.log(
              "Topology graph updated successfully, update count:",
              forceUpdateRef.current
            );
          } catch (error) {
            console.error("Error refreshing edge animations:", error);
          }
        }
      }, 100);

      // Store timeout ID for potential cleanup
      return () => clearTimeout(animationTimeoutId);
    } catch (error) {
      console.error("Error updating topology graph:", error);
      // If update fails, force a re-render
      setIsRendered(false);
    }
  }, [tGraph, gData, createData, isRendered]);

  // Force refresh when window gains focus (to catch external changes)
  useEffect(() => {
    const handleFocus = () => {
      console.log("Window gained focus, refreshing topology data");
      dispatch(getTopologyData());
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [dispatch]);

  // Initial Data Load
  useEffect(() => {
    dispatch(getTopologyData());

    // Set up periodic cleanup of old topology data (every 10 minutes)
    const cleanupInterval = setInterval(() => {
      dispatch(cleanupOldTopologyData());
    }, 10 * 60 * 1000);

    return () => {
      clearInterval(cleanupInterval);
    };
  }, [dispatch]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (tGraph && !tGraph.destroyed) {
        try {
          // Unregister from memory optimizer first
          graphMemoryOptimizer.unregisterGraph(tGraph);

          // Remove all event listeners before destroying
          if (typeof tGraph.off === "function") {
            try {
              tGraph.off();
            } catch (offError) {
              console.warn(
                "Error removing TopologyPage graph event listeners:",
                offError
              );
            }
          }

          // Clear all data to free memory
          if (typeof tGraph.clear === "function") {
            try {
              tGraph.clear();
            } catch (clearError) {
              console.warn(
                "Error clearing TopologyPage graph data:",
                clearError
              );
            }
          }

          // Check if graph is still valid before destroying
          if (!tGraph.destroyed && typeof tGraph.destroy === "function") {
            try {
              // Pre-cleanup DOM elements to prevent removeChild errors
              const container = document.getElementById("topology-container");
              if (container && container.children.length > 0) {
                const canvasElements =
                  container.querySelectorAll("canvas, svg");
                canvasElements.forEach((element) => {
                  try {
                    if (element.parentNode) {
                      element.parentNode.removeChild(element);
                    }
                  } catch (e) {
                    // Ignore individual element removal errors
                  }
                });
              }

              tGraph.destroy();
              console.log(
                "TopologyPage graph destroyed successfully on unmount"
              );
            } catch (destroyError) {
              console.warn(
                "Error during TopologyPage graph destroy:",
                destroyError
              );
              // Force mark as destroyed to prevent further operations
              if (tGraph) {
                tGraph.destroyed = true;
              }
            }
          }
        } catch (error) {
          console.error(
            "Error destroying TopologyPage graph on unmount:",
            error
          );
        }
      }
    };
  }, [tGraph]);

  // Client Selection Handler
  const handleSelectChange = useCallback(
    (value) => {
      dispatch(getGraphDataOnClientChange(value));
      setIsRendered(false);
    },
    [dispatch]
  );

  // API Handlers
  const handleAddTopology = useCallback(
    async (values) => {
      try {
        const data = createManualTpologyData(values);
        await addTopology(data).unwrap();
        notification.success({
          message: "Successfully added topology!",
        });
        handleRefresh();
      } catch (error) {
        const errorMessage =
          error?.data?.error ||
          error?.data ||
          error?.message ||
          "Failed to add topology";
        notification.error({ message: errorMessage });
      } finally {
        setOpenAddtopoForm(false);
      }
    },
    [addTopology, notification, handleRefresh]
  );

  const handleSaveRestoreTopology = useCallback(
    async (values) => {
      if (!actionType) {
        setOpenSaveRestore(false);
        notification.error({ message: "Action type is missing!" });
        return;
      }

      try {
        await saveRestoreTopology({
          filename: `${values.filename}.json`,
          actionType,
        }).unwrap();

        notification.success({
          message: `Successfully ${actionType}d topology!`,
        });
        handleRefresh();
      } catch (error) {
        const errorMessage =
          error?.data?.error ||
          error?.data ||
          error?.message ||
          `Failed to ${actionType} topology`;
        notification.error({ message: errorMessage });
      } finally {
        setOpenSaveRestore(false);
        setActionType("");
      }
    },
    [actionType, saveRestoreTopology, notification, handleRefresh]
  );

  return (
    <>
      <Card
        variant="borderless"
        title="Device Topology"
        styles={{
          body: {
            padding: 8,
            position: "relative",
            width: "100%",
            height: `calc(100vh - 145px)`,
          },
        }}
        extra={
          <Flex gap={10} align="center" wrap="wrap">
            {clientsData.length > 0 && (
              <Select
                defaultValue="All Network Service"
                style={{
                  width: 240,
                }}
                value={reqClient}
                onChange={handleSelectChange}
                options={clientsData.map((item) => ({
                  value: item,
                  label: item,
                }))}
              />
            )}

            <Tooltip title="Refresh">
              <Button
                type="text"
                onClick={handleRefresh}
                icon={<ReloadOutlined />}
              />
            </Tooltip>

            <Tooltip title="Add manual topology">
              <Button
                type="text"
                onClick={() => setOpenAddtopoForm(true)}
                icon={<PlusOutlined />}
              />
            </Tooltip>

            <Tooltip title="Save manual topology">
              <Button
                type="text"
                onClick={() => {
                  setActionType("save");
                  setOpenSaveRestore(true);
                }}
                icon={<FileAddOutlined />}
              />
            </Tooltip>

            <Tooltip title="Restore manual topology">
              <Button
                type="text"
                onClick={() => {
                  setActionType("restore");
                  setOpenSaveRestore(true);
                }}
                icon={<FileSyncOutlined />}
              />
            </Tooltip>

            <Checkbox onChange={handleEditModeChange} checked={isEditMode}>
              Edit Node Position
            </Checkbox>

            {isEditMode ? (
              <Button type="primary" onClick={handleSaveNodePosition}>
                Save Node Position
              </Button>
            ) : (
              <>
                <Checkbox onChange={handlePhysicsChange} checked={gPhysics}>
                  Physics
                </Checkbox>
                <Button type="primary" onClick={handleFitView}>
                  Fit View
                </Button>
              </>
            )}

            <Button type="primary" onClick={handleDownloadImage}>
              Export Toplogy
            </Button>
          </Flex>
        }
      >
        <div
          id="topology-container"
          style={{
            position: "relative",
            border: "1px solid gray",
            overflow: "hidden",
            width: "100%",
            height: "100%",
            background: token.colorBgContainer,
          }}
        ></div>
      </Card>
      <ManualTopoForm
        open={openAddtopoForm}
        onCancel={() => setOpenAddtopoForm(false)}
        onOk={handleAddTopology}
        loading={topoLoading}
      />

      <ManualTopoSaveRestoreForm
        open={openSaveRestore}
        onCancel={() => setOpenSaveRestore(false)}
        onOk={handleSaveRestoreTopology}
        loading={saveResLoading}
      />
    </>
  );
};

export default TopologyPage;
