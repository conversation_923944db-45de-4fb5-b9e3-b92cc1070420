package mnms

import (
	"archive/zip"
	"bufio"
	"bytes"
	"fmt"
	"io"
	"math"
	"net"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/qeof/q"
)

const (
	firmwarePort            = 55950
	packetSize              = 512
	firmwareUpgradeTimeout  = 600 * time.Second
	firmwareResponseTimeout = 5 * time.Second
	firmwareConnTimeout     = 10 * time.Second
)

type fwStatus int

func firmwarePacket() []byte {
	packet := make([]byte, 40)
	def := "name1234passwd12modelname 123456" // not important, just input anychar
	for i, v := range def {
		packet[i] = byte(v)
	}
	packet[36] = 0x72
	return packet
}

const (
	ready fwStatus = iota
	erased
	finish
	going
)

func (p fwStatus) String() string {
	switch p {
	case ready:
		return "E001"
	case erased:
		return "S001"
	case finish:
		return "S002"
	case going:
		return "a"
	}
	return "unknow"
}

type Firmware struct {
	ip       string
	filesize int64
	status   string
	r        io.Reader
	mac      string
}

// GetProcessStatus get status of upgrading fw,process ,percent
func (f *Firmware) GetProcessStatus() string {
	return f.status
}

// This section covers firmware updates via GWD.
//
// For updates via agent, see the "HandleAgentFirmware" function in http.go.
//
// For devices without HTTP support, firmware updates are performed using
//
//	the agent TCP method, see the "startTCPServer" function in agent.go.
func (f *Firmware) GwdUpgrading(url string) error {
	// init status
	f.status = "Uploading"

	q.Q("Uploading file to", f.ip)
	// start upgrading fw
	go func() {
		// download url file to data
		data, err := ReadFirmwareFile(url)
		if err != nil {
			q.Q(err)
			f.status = "error: Download file fail"
			return
		}
		f.filesize = int64(len(data))
		f.r = bytes.NewReader(data)
		q.Q(f.ip, f.filesize)

		address := strings.Join([]string{f.ip, strconv.Itoa(firmwarePort)}, ":")

		conn, err := net.DialTimeout("tcp", address, firmwareConnTimeout)
		if err != nil {
			q.Q(err)
			f.status = "error: Can not connect to " + address
			return
		}
		defer conn.Close()

		// send fw header packet
		_, err = conn.Write(downloadRequest(f.filesize))
		if err != nil {
			q.Q(err)
			f.status = fmt.Sprintf("error: Failed to send firmware header: %v", err)
			return
		}
		err = f.waitForResponse(conn, going)
		if err != nil {
			q.Q(err)
			// Status already set in waitForResponse
			return
		}

		packetCount := 0
		var unfinishProcess map[int]int = map[int]int{30: 30, 60: 60, 90: 90, 100: 100}
		// send file
		buf := make([]byte, 0, packetSize)
		for {
			// wait uploading fw
			n, readerr := io.ReadFull(f.r, buf[:cap(buf)])
			// calculate percent
			packetCount++
			unfinishProcess = f.calculateProcess(packetCount, unfinishProcess)
			// uploading process
			buf = buf[:n]
			_, err := conn.Write(buf)
			if err != nil {
				q.Q(err)
				f.status = fmt.Sprintf("error: Failed to send firmware data: %v", err)
				return
			}
			err = f.waitForResponse(conn, going)
			if err != nil {
				q.Q(err)
				// Status already set in waitForResponse
				return
			}
			// wait updraging fw
			if readerr != nil {
				if readerr == io.EOF || readerr == io.ErrUnexpectedEOF {
					err := f.waitForResponse(conn, erased)
					if err != nil {
						q.Q(err)
						// For timeout during erase, the device might still be working
						if strings.Contains(err.Error(), "timeout") {
							f.status = "info: Firmware uploaded, device processing (may take longer on weak CPU)"
							q.Q("Firmware upload completed, device may still be processing the upgrade")
						}
						// Status already set in waitForResponse
						return
					}
					break
				}
			}
		}
		// wait finish
		err = f.waitForResponse(conn, finish)
		if err != nil {
			q.Q(err)
			// For timeout during finish, the device might still be completing the upgrade
			if strings.Contains(err.Error(), "timeout") {
				f.status = "info: Firmware upgrade may still be completing (device has weak CPU)"
				q.Q("Firmware upgrade may still be completing on device with weak CPU")
			}
			// Status already set in waitForResponse
			return
		}
		conn.Close()
	}()
	return nil
}

// waitForResponse waits for firmware response and updates status consistently
func (f *Firmware) waitForResponse(conn net.Conn, expectedStatus fwStatus) error {
	// Set appropriate timeout based on expected status
	var timeout time.Duration
	if expectedStatus == going {
		timeout = firmwareResponseTimeout
	} else {
		timeout = firmwareUpgradeTimeout
	}

	err := conn.SetReadDeadline(time.Now().Add(timeout))
	if err != nil {
		f.status = fmt.Sprintf("error: Failed to set read deadline: %v", err)
		return fmt.Errorf("failed to set read deadline: %w", err)
	}

	responseBuffer := make([]byte, len(expectedStatus.String()))
	for {
		_, err := conn.Read(responseBuffer)
		if err != nil {
			// Check if this is a timeout error
			if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
				// For upgrade operations, timeouts are expected due to weak CPU processing
				if expectedStatus == erased || expectedStatus == finish {
					f.status = fmt.Sprintf("info: Device may still processing (timeout after %v)", timeout)
					return fmt.Errorf("timeout waiting for firmware response (device may still be processing): %w", err)
				}
			}
			// For other errors or non-timeout cases, this is a real failure
			f.status = fmt.Sprintf("error: Failed to read response: %v", err)
			return fmt.Errorf("failed to read firmware response: %w", err)
		}

		response := strings.TrimSpace(string(responseBuffer))
		if response == expectedStatus.String() {
			// Update status based on the firmware state
			switch expectedStatus {
			case erased:
				f.status = "Upgrading"
			case finish:
				f.status = "Complete"
			case going:
				// Keep current status for ongoing operations
			case ready:
				f.status = "Ready"
			}
			return nil
		}
		// Continue reading if response doesn't match expected status
	}
}

// calculateProcess calculate file percent
func (f *Firmware) calculateProcess(packetCount int, unfinishProcess map[int]int) map[int]int {
	proc := packetCount * 100 / int(math.Ceil(float64(f.filesize)/packetSize))
	progressPercent := int(math.Floor(float64(proc)*100) / 100)
	// send percent
	_, ok := unfinishProcess[progressPercent]
	if ok {
		delete(unfinishProcess, progressPercent)
		if progressPercent == 100 {
			err := SendSyslog(LOG_NOTICE, "firmware", f.mac+" upgrading device")
			if err != nil {
				q.Q(err)
			}
		} else {
			err := SendSyslog(LOG_NOTICE, "firmware", f.mac+" uploading file to device "+strconv.Itoa(progressPercent)+"%")
			if err != nil {
				q.Q(err)
			}
		}
		q.Q(progressPercent)
	}
	return unfinishProcess
}

func downloadRequest(filesize int64) []byte {
	dl_request := firmwarePacket()
	// dl_request[32] ~ dl_request[35] :save file size
	for j := 3; j >= 0; j-- {
		dl_request[j+32] = (byte)(filesize / int64(math.Pow(256, float64(j))))
		filesize = filesize - int64(dl_request[j+32])*int64(math.Pow(256, float64(j)))
	}
	return dl_request
}

func ReadFirmwareFile(url string) ([]byte, error) {
	var err error

	needTrimString := ""
	if runtime.GOOS == "windows" {
		needTrimString = "file:///"
	} else {
		needTrimString = "file://"
	}
	noPrefixUrl := strings.TrimPrefix(url, needTrimString)
	// open local file
	fd, err := os.Open(noPrefixUrl)
	if err != nil {
		return nil, err
	}
	defer fd.Close()

	reader := bufio.NewReader(fd)

	data, err := io.ReadAll(reader)
	if err != nil {
		return nil, err
	}

	//create to file
	//_ = os.WriteFile(filepath, data, 0755)
	//unzip
	num := int64(len(data))
	zipReader, err := zip.NewReader(bytes.NewReader(data), num)
	if err != nil {
		q.Q("warning: not a valid zip file")
		return data, nil
	}
	if len(zipReader.File) > 1 {
		return nil, fmt.Errorf("error: unzip files is empty")
	}
	file := zipReader.File[0]
	f, err := file.Open()
	if err != nil {
		return nil, err
	}
	defer f.Close()
	dataunzip, err2 := io.ReadAll(f)
	if err2 != nil {
		return nil, err2
	}
	return dataunzip, nil
}

// Upgrade firmware.
// low level command (Gwd)
//
// Usage : gwd firmware update [mac address] [file url]
//
//	[mac address] : target device mac address
//	[file url]    : file url
//
// Example :
//
//	gwd firmware update AA-BB-CC-DD-EE-FF file:///C:/Users/<USER>/Downloads/xxxxx.dld
//	gwd firmware update AA-BB-CC-DD-EE-FF file:///tmp/downloads/xxx.dld
func GwdFirmwareCmd(cmdinfo *CmdInfo) *CmdInfo {
	cmd := cmdinfo.Command
	standardMac := cmdinfo.DevId
	ws := strings.Split(cmd, " ")
	dev, err := FindDev(standardMac)
	if err != nil {
		cmdinfo.Status = "error: device not found"
		return cmdinfo
	}
	err = CheckDeviceLock(standardMac)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("info: %v", err)
		return cmdinfo
	}
	ip := dev.IPAddress
	// validate ip
	err = CheckIPAddress(ip)
	if err != nil {
		cmdinfo.Status = fmt.Sprintf("error: %v", err)
		return cmdinfo
	}

	url := ws[4]
	if !strings.HasPrefix(url, "file:///") {
		cmdinfo.Status = "error: only support file:/// scheme"
		return cmdinfo
	}
	needTrimString := ""
	if runtime.GOOS == "windows" {
		needTrimString = "file:///"
	} else {
		needTrimString = "file://"
	}
	noPrefixUrl := strings.TrimPrefix(url, needTrimString)
	if !FileExists(noPrefixUrl) {
		cmdinfo.Status = "error: could not find file in path"
		return cmdinfo
	}

	// create new  device for firmware
	device := Firmware{ip: ip, status: "", mac: standardMac}
	cmdinfo.Status = "ok"
	go gwdUpgradeFw(device, url, cmdinfo)
	return cmdinfo
}

// upgradefw
func gwdUpgradeFw(device Firmware, url string, cmdinfo *CmdInfo) {
	LockDev(device.mac)
	defer unLockDev(device.mac)
	err := device.GwdUpgrading(url)
	if err != nil {
		fmt.Println(err.Error())
	}
	var messages string = ""
	for {
		time.Sleep(time.Duration(time.Second * 1))
		msgs := device.GetProcessStatus()

		if strings.HasPrefix(msgs, "error") {
			messages = "device: " + device.ip + ", Messages: " + msgs
			q.Q(messages)
			cmdinfo.Status = msgs
			err := SendSyslog(LOG_NOTICE, "firmware", device.mac+" "+cmdinfo.Status)
			if err != nil {
				q.Q(err)
			}
			return
		}
		if strings.HasPrefix(msgs, "info:") && strings.Contains(msgs, "timeout") {
			// Device may still be processing, log this but continue monitoring for a bit longer
			messages = "device: " + device.ip + ", Messages: " + msgs
			q.Q(messages)
			cmdinfo.Status = msgs
			err := SendSyslog(LOG_NOTICE, "firmware", device.mac+" "+cmdinfo.Status)
			if err != nil {
				q.Q(err)
			}
			// Give device more time to complete processing
			time.Sleep(30 * time.Second)
			continue
		}
		if msgs == "Complete" {
			messages = "device:" + device.ip + ", Messages:" + msgs
			q.Q(messages)
			cmdinfo.Status = "ok"
			err := SendSyslog(LOG_NOTICE, "firmware", device.mac+" "+cmdinfo.Status)
			if err != nil {
				q.Q(err)
			}
			return
		}
	}
}
