import React, { useState, useEffect } from "react";
import {
  Card,
  Button,
  Typography,
  Space,
  Progress,
  Statistic,
  Row,
  Col,
} from "antd";
import {
  DeleteOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from "@ant-design/icons";
import memoryMonitor from "../../utils/memoryMonitor";

const { Title } = Typography;

const MemoryMonitorWidget = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentMeasurement, setCurrentMeasurement] = useState(null);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== "development") return;

    // Check monitoring status periodically since auto-start has a delay
    const checkStatus = () => {
      const isActive = !!memoryMonitor.intervalId;
      setIsMonitoring(isActive);

      // Take a measurement if monitoring is active
      if (isActive) {
        const measurement = memoryMonitor.takeMeasurement();
        if (measurement) {
          setCurrentMeasurement(measurement);
        }
      }
    };

    // Initial check
    checkStatus();

    // Update current measurement and status every 5 seconds
    const updateInterval = setInterval(checkStatus, 5000);

    return () => clearInterval(updateInterval);
  }, []);

  // Don't render in production
  if (process.env.NODE_ENV !== "development") return null;

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      memoryMonitor.stop();
      setIsMonitoring(false);
    } else {
      memoryMonitor.start();
      setIsMonitoring(true);
    }
  };

  const handleCleanup = () => {
    memoryMonitor.triggerCleanup();
    // Force a new measurement after cleanup
    setTimeout(() => {
      const measurement = memoryMonitor.takeMeasurement();
      if (measurement) {
        setCurrentMeasurement(measurement);
      }
    }, 1000);
  };

  const handleGetReport = () => {
    const report = memoryMonitor.getReport();
    console.log("📊 Memory Monitor Report:", report);
  };

  const formatBytes = (bytes) => {
    if (!bytes) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
  };

  const getMemoryStatus = (percentage) => {
    if (percentage > 80)
      return { status: "exception", icon: <WarningOutlined /> };
    if (percentage > 60) return { status: "active", icon: <WarningOutlined /> };
    return { status: "success", icon: <CheckCircleOutlined /> };
  };

  if (!isVisible) {
    return (
      <div
        style={{
          position: "fixed",
          bottom: 80,
          right: 20,
          zIndex: 9999,
          background: "#1890ff",
          color: "white",
          padding: "8px 12px",
          borderRadius: "4px",
          cursor: "pointer",
          fontSize: "12px",
          fontWeight: "bold",
        }}
        onClick={() => setIsVisible(true)}
      >
        🔍 Memory Monitor
      </div>
    );
  }

  const memory = currentMeasurement?.memory;
  const dom = currentMeasurement?.dom;
  const storage = currentMeasurement?.storage;

  return (
    <Card
      title="🔍 Memory Monitor (Dev Only)"
      size="small"
      style={{
        position: "fixed",
        bottom: 70,
        right: 20,
        width: 350,
        zIndex: 9999,
        maxHeight: "80vh",
        overflow: "auto",
      }}
      extra={
        <Button type="text" size="small" onClick={() => setIsVisible(false)}>
          ×
        </Button>
      }
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        {/* Controls */}
        <Row gutter={8}>
          <Col span={8}>
            <Button
              size="small"
              type={isMonitoring ? "default" : "primary"}
              onClick={handleToggleMonitoring}
              block
            >
              {isMonitoring ? "Stop" : "Start"}
            </Button>
          </Col>
          <Col span={8}>
            <Button
              size="small"
              icon={<DeleteOutlined />}
              onClick={handleCleanup}
              block
            >
              Cleanup
            </Button>
          </Col>
          <Col span={8}>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleGetReport}
              block
            >
              Report
            </Button>
          </Col>
        </Row>

        {/* Memory Usage */}
        {memory && (
          <div>
            <Title level={5} style={{ margin: "8px 0 4px 0" }}>
              Memory Usage
            </Title>
            <Progress
              percent={memory.percentage}
              {...getMemoryStatus(memory.percentage)}
              format={() => `${memory.percentage}%`}
            />
            <Row gutter={16} style={{ marginTop: 8 }}>
              <Col span={12}>
                <Statistic
                  title="Used"
                  value={formatBytes(memory.used)}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="Limit"
                  value={formatBytes(memory.limit)}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
            </Row>
          </div>
        )}

        {/* DOM Info */}
        {dom && (
          <div>
            <Title level={5} style={{ margin: "8px 0 4px 0" }}>
              DOM Elements
            </Title>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="Elements"
                  value={dom.elements}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Listeners"
                  value={dom.eventListeners}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Tooltips"
                  value={dom.tooltips}
                  valueStyle={{
                    fontSize: "12px",
                    color: dom.tooltips > 0 ? "#ff4d4f" : undefined,
                  }}
                />
              </Col>
            </Row>
          </div>
        )}

        {/* Storage Info */}
        {storage && (
          <div>
            <Title level={5} style={{ margin: "8px 0 4px 0" }}>
              Storage Usage
            </Title>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="localStorage"
                  value={formatBytes(storage.localStorage)}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="sessionStorage"
                  value={formatBytes(storage.sessionStorage)}
                  valueStyle={{ fontSize: "12px" }}
                />
              </Col>
            </Row>
          </div>
        )}

        {/* Status */}
        <div style={{ fontSize: "11px", color: "#666" }}>
          Status: {isMonitoring ? "🟢 Monitoring" : "🔴 Stopped"}
          {currentMeasurement && (
            <div>
              Last update:{" "}
              {new Date(currentMeasurement.timestamp).toLocaleTimeString()}
            </div>
          )}
        </div>

        {/* Tips */}
        <div style={{ fontSize: "10px", color: "#999", marginTop: 8 }}>
          💡 Tips: Open browser console for detailed logs and reports
        </div>
      </Space>
    </Card>
  );
};

export default MemoryMonitorWidget;
