package mnms

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"testing"
)

func TestAgent(t *testing.T) {
	// init encryption
	encryption, _ := NewEncryptionContext()

	fakeHelloMsg := fmt.Sprintf("{\"kind\":\"announce\",\"ip\":\"%s\",\"url\":\"none\",\"version\":\"test\"}", "*************")
	jsonBytes := []byte(fakeHelloMsg)
	encryptionText, _, _ := EncryptCipherText(encryption.sharedSecret, encryption.nonce, jsonBytes, len(jsonBytes))
	// encode base64
	b64_text := base64.StdEncoding.EncodeToString(encryptionText)

	// decode base64
	encryption_messages, _ := base64.StdEncoding.DecodeString(b64_text)
	// decryption
	//encryption_messages_str := string(messages[:lenMessages])
	//encryption_messages := stringToByteHex(encryption_messages_str)
	plain_text, _ := DecryptCipherText(*encryption, encryption_messages)
	// fill AgentEthernetFrame
	var msg AgentMessages
	err := json.Unmarshal(plain_text[:], &msg)
	if err != nil {
		t.Fatalf("error: %v", err)
		return
	}
	if !strings.HasPrefix(msg.Kind, "announce") {
		t.Fatalf("error: %v", err)
	}
}

func TestCheckBbnimAndAgentVersion(t *testing.T) {
	// Treat QC.AgentSupMaxVersion as static; only adjust QC.Version during tests
	origVersion := QC.Version
	defer func() { QC.Version = origVersion }()

	tests := []struct {
		name         string
		bbnimVersion string
		agentVersion string
		expectMsg    string
	}{
		{
			name:         "equal_versions_supported",
			bbnimVersion: "v1.0.11",
			agentVersion: "v1.0.5",
			expectMsg:    "bbnim v1.0.11 supports agent v1.0.5. Versions are compatible.",
		},
		{
			name:         "agent_newer_than_supported",
			bbnimVersion: "v1.0.11",
			agentVersion: "v1.0.6",
			expectMsg:    "bbnim v1.0.11 supports agent up to v1.0.5. Your agent version is v1.0.6. Please update bbnim.",
		},
		{
			name:         "agent_older_than_supported",
			bbnimVersion: "v1.0.11",
			agentVersion: "v1.0.4",
			expectMsg:    "bbnim v1.0.11 supports agent up to v1.0.5. Your agent version is v1.0.4. Please update the agent.",
		},
		{
			name:         "unknown_bbnim_version",
			bbnimVersion: "v9.9.9",
			agentVersion: "v1.0.1",
			expectMsg:    "bbnim version v9.9.9 does not support agent compatibility check. Please update bbnim.",
		},
		{
			name:         "invalid_agent_version",
			bbnimVersion: "v1.0.11",
			agentVersion: "bad.version",
			expectMsg:    "Agent version bad.version is invalid. Please update the agent.",
		},
		{
			name:         "old_bbnim_version_not_in_map",
			bbnimVersion: "v1.0.1",
			agentVersion: "v1.0.5",
			expectMsg:    "bbnim version v1.0.1 does not support agent compatibility check. Please update bbnim.",
		},
		{
			name:         "agent_much_older_than_supported",
			bbnimVersion: "v1.0.9",
			agentVersion: "v1.0.2",
			expectMsg:    "bbnim v1.0.9 supports agent up to v1.0.4. Your agent version is v1.0.2. Please update the agent.",
		},
		{
			name:         "exact_match_different_bbnim_version",
			bbnimVersion: "v1.0.9",
			agentVersion: "v1.0.4",
			expectMsg:    "bbnim v1.0.9 supports agent v1.0.4. Versions are compatible.",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			QC.Version = tc.bbnimVersion
			msg := buildAgentVersionCheckMessage(QC.Version, tc.agentVersion)
			if msg != tc.expectMsg {
				t.Fatalf("unexpected message.\n got:  %q\n want: %q", msg, tc.expectMsg)
			}
			// Also call the function under test but ignore its error (syslog may fail in test env)
			_ = CheckBbnimAndAgentVersion(tc.agentVersion)
		})
	}
}
