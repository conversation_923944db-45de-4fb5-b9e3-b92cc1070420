import {
  createAsyncThunk,
  createSelector,
  createSlice,
} from "@reduxjs/toolkit";
import protectedApis from "../../utils/apis/protectedApis";
import {
  ParseJsonToTopoData,
  getAllTopologyData,
  getTopologyClient,
  getTopologyDataByClient,
} from "../../utils/comman/dataMapping";

const prevNmsTopoNodesData = JSON.parse(
  localStorage.getItem("prevNmsTopoNodesData")
);

export const getTopologyData = createAsyncThunk(
  "newTopologySlice/getTopologyData",
  async (_, thunkAPI) => {
    try {
      const response = await protectedApis.get("/api/v1/topology", {});
      const data = await response.data;
      if (response.status === 200) {
        console.log("topology data1", data);
        return data;
      } else {
        return thunkAPI.rejectWithValue(data);
      }
    } catch (e) {
      if (e.response && e.response.data !== "") {
        return thunkAPI.rejectWithValue(e.response.data);
      } else return thunkAPI.rejectWithValue(e.message);
    }
  }
);

const newTopologySlice = createSlice({
  name: "newTopologySlice",
  initialState: {
    isEditMode: false,
    gPhysics: false,
    clientsData: [],
    topologyData: {},
    reqClient: "All Network Service",
    gData: { nodes: [], edges: [] },
    savedNodes: prevNmsTopoNodesData === null ? [] : prevNmsTopoNodesData,
  },
  reducers: {
    setIsEditMode: (state, { payload }) => {
      state.isEditMode = payload;
    },
    setGPhysics: (state, { payload }) => {
      state.gPhysics = payload;
    },
    saveNodesPosition: (state, { payload }) => {
      // Merge new nodes with existing ones, avoiding duplicates
      const newNodesMap = new Map(
        state.savedNodes.map((item) => [item["id"], item])
      );

      // Update with new payload data
      payload.forEach((node) => {
        newNodesMap.set(node.id, node);
      });

      // Convert back to array and limit to prevent memory bloat (keep last 1000 nodes)
      const arrayUniqueByKey = Array.from(newNodesMap.values()).slice(-1000);
      state.savedNodes = arrayUniqueByKey;

      try {
        const dataToStore = JSON.stringify(arrayUniqueByKey);
        // Only store if data size is reasonable (< 500KB)
        if (dataToStore.length < 500000) {
          localStorage.setItem("prevNmsTopoNodesData", dataToStore);
        } else {
          console.warn("Topology nodes data too large, limiting storage");
          // Keep only the last 500 nodes if data is too large
          const limitedData = arrayUniqueByKey.slice(-500);
          state.savedNodes = limitedData;
          localStorage.setItem(
            "prevNmsTopoNodesData",
            JSON.stringify(limitedData)
          );
        }
      } catch (error) {
        console.error("Failed to store topology nodes in localStorage:", error);
        // Clear localStorage if it's full
        localStorage.removeItem("prevNmsTopoNodesData");
      }
    },
    getGraphDataOnClientChange: (state, { payload }) => {
      state.reqClient = payload;
      const { nodes, links } =
        payload === "All Network Service"
          ? getAllTopologyData(state.topologyData)
          : getTopologyDataByClient(state.topologyData, payload);
      state.gData = { nodes, edges: links };
    },
    // Add cleanup action for old topology data
    cleanupOldTopologyData: (state) => {
      // Clear old saved nodes (keep only last 100)
      if (state.savedNodes.length > 100) {
        state.savedNodes = state.savedNodes.slice(-100);
        try {
          localStorage.setItem(
            "prevNmsTopoNodesData",
            JSON.stringify(state.savedNodes)
          );
        } catch (error) {
          console.error(
            "Failed to update localStorage after topology cleanup:",
            error
          );
          localStorage.removeItem("prevNmsTopoNodesData");
        }
      }

      // Clear graph data to free memory
      if (
        state.gData &&
        (state.gData.nodes?.length > 1000 || state.gData.edges?.length > 1000)
      ) {
        console.log("Cleaning up large topology graph data");
        state.gData = { nodes: [], edges: [] };
      }
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getTopologyData.fulfilled, (state, { payload }) => {
      state.topologyData = ParseJsonToTopoData(payload);
      state.clientsData = [
        "All Network Service",
        ...getTopologyClient(ParseJsonToTopoData(payload)),
      ];
      const { nodes, links } =
        state.reqClient === "All Network Service"
          ? getAllTopologyData(ParseJsonToTopoData(payload))
          : getTopologyDataByClient(
              ParseJsonToTopoData(payload),
              state.reqClient
            );
      state.gData = { nodes, edges: links };
    });
  },
});

export const {
  setIsEditMode,
  setGPhysics,
  saveNodesPosition,
  getGraphDataOnClientChange,
  cleanupOldTopologyData,
} = newTopologySlice.actions;

export const newTopologySelector = createSelector(
  (state) => state.newTopology,
  ({ isEditMode, gPhysics, gData, savedNodes, clientsData, reqClient }) => ({
    isEditMode,
    gPhysics,
    gData,
    savedNodes,
    clientsData,
    reqClient,
  })
);

export default newTopologySlice;
