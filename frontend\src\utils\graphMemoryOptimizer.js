// G6 Graph Memory Optimizer
// Helps optimize memory usage for G6 graphs

class GraphMemoryOptimizer {
  constructor() {
    this.graphInstances = new Set();
    this.cleanupInterval = null;
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  // Register a graph instance for monitoring
  registerGraph(graph, name = 'unknown') {
    if (!graph) return;
    
    graph._memoryOptimizerName = name;
    graph._memoryOptimizerRegistered = Date.now();
    this.graphInstances.add(graph);
    
    console.log(`📊 Graph Optimizer: Registered graph "${name}"`);
    
    // Start cleanup interval if this is the first graph
    if (this.graphInstances.size === 1 && !this.cleanupInterval) {
      this.startPeriodicCleanup();
    }
  }

  // Unregister a graph instance
  unregisterGraph(graph) {
    if (!graph) return;
    
    const name = graph._memoryOptimizerName || 'unknown';
    this.graphInstances.delete(graph);
    
    console.log(`📊 Graph Optimizer: Unregistered graph "${name}"`);
    
    // Stop cleanup if no graphs remain
    if (this.graphInstances.size === 0 && this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  // Start periodic cleanup
  startPeriodicCleanup() {
    if (!this.isEnabled || this.cleanupInterval) return;
    
    console.log('📊 Graph Optimizer: Starting periodic cleanup...');
    
    this.cleanupInterval = setInterval(() => {
      this.optimizeAllGraphs();
    }, 2 * 60 * 1000); // Every 2 minutes
  }

  // Optimize all registered graphs
  optimizeAllGraphs() {
    console.log(`📊 Graph Optimizer: Optimizing ${this.graphInstances.size} graphs...`);
    
    let optimizedCount = 0;
    const graphsToRemove = [];
    
    this.graphInstances.forEach(graph => {
      try {
        if (graph.destroyed || !graph.get) {
          graphsToRemove.push(graph);
          return;
        }
        
        if (this.optimizeGraph(graph)) {
          optimizedCount++;
        }
      } catch (error) {
        console.warn('📊 Graph Optimizer: Error optimizing graph:', error);
        graphsToRemove.push(graph);
      }
    });
    
    // Remove destroyed graphs
    graphsToRemove.forEach(graph => {
      this.graphInstances.delete(graph);
    });
    
    if (optimizedCount > 0) {
      console.log(`📊 Graph Optimizer: Optimized ${optimizedCount} graphs`);
    }
  }

  // Optimize a single graph
  optimizeGraph(graph) {
    if (!graph || graph.destroyed) return false;
    
    let optimized = false;
    const name = graph._memoryOptimizerName || 'unknown';
    
    try {
      // Get current data
      const data = graph.save();
      const nodeCount = data.nodes?.length || 0;
      const edgeCount = data.edges?.length || 0;
      
      // Skip if graph is empty
      if (nodeCount === 0 && edgeCount === 0) return false;
      
      // Optimize large graphs
      if (nodeCount > 500 || edgeCount > 1000) {
        console.log(`📊 Optimizing large graph "${name}": ${nodeCount} nodes, ${edgeCount} edges`);
        
        // Clear and re-render to free memory
        graph.clear();
        graph.data(data);
        graph.render();
        
        optimized = true;
      }
      
      // Clean up edge animations periodically
      const edges = graph.getEdges();
      if (edges.length > 0) {
        edges.forEach(edge => {
          try {
            // Reset animation states to prevent memory leaks
            edge.clearStates();
          } catch (e) {
            // Ignore errors for individual edges
          }
        });
      }
      
      // Force layout recalculation for force layouts to prevent accumulation
      const layoutType = graph.get('layout')?.type;
      if (layoutType === 'force' && nodeCount > 100) {
        graph.updateLayout({
          type: 'force',
          preventOverlap: true,
          nodeSize: 30,
        });
        optimized = true;
      }
      
    } catch (error) {
      console.warn(`📊 Graph Optimizer: Error optimizing graph "${name}":`, error);
    }
    
    return optimized;
  }

  // Manual optimization trigger
  optimizeNow() {
    console.log('📊 Graph Optimizer: Manual optimization triggered');
    this.optimizeAllGraphs();
  }

  // Get statistics about registered graphs
  getStats() {
    const stats = {
      totalGraphs: this.graphInstances.size,
      graphs: [],
      totalNodes: 0,
      totalEdges: 0,
    };
    
    this.graphInstances.forEach(graph => {
      try {
        if (graph.destroyed || !graph.get) return;
        
        const data = graph.save();
        const nodeCount = data.nodes?.length || 0;
        const edgeCount = data.edges?.length || 0;
        
        stats.graphs.push({
          name: graph._memoryOptimizerName || 'unknown',
          registered: graph._memoryOptimizerRegistered,
          nodes: nodeCount,
          edges: edgeCount,
          renderer: graph.get('renderer'),
        });
        
        stats.totalNodes += nodeCount;
        stats.totalEdges += edgeCount;
      } catch (error) {
        // Skip problematic graphs
      }
    });
    
    return stats;
  }

  // Clean up all graphs and stop monitoring
  destroy() {
    console.log('📊 Graph Optimizer: Destroying optimizer...');
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.graphInstances.clear();
  }
}

// Create singleton instance
const graphMemoryOptimizer = new GraphMemoryOptimizer();

// Add global access for debugging
if (process.env.NODE_ENV === 'development') {
  window.graphMemoryOptimizer = graphMemoryOptimizer;
  console.log('📊 Graph Memory Optimizer: Available globally as window.graphMemoryOptimizer');
  console.log('📊 Commands: optimizeNow(), getStats(), registerGraph(graph, name), unregisterGraph(graph)');
}

export default graphMemoryOptimizer;
