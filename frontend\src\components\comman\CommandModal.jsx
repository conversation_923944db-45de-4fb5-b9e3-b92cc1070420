import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, Space, <PERSON><PERSON><PERSON>, Typo<PERSON> } from "antd";
import React, { useState } from "react";
import {
  useGetAllCommandsQuery,
  useSendCommandMutation,
} from "../../app/services/commandApi";
import { ProTable } from "@ant-design/pro-components";
import dayjs from "dayjs";
import { DeleteOutlined, RetweetOutlined } from "@ant-design/icons";

const CommandModal = ({ open, onCancel }) => {
  const { notification } = App.useApp();
  const [inputSearch, setInputSearch] = useState("");
  const { data: cmdData = [], refetch } = useGetAllCommandsQuery(
    {},
    { refetchOnMountOrArgChange: true }
  );

  const [sendCommand] = useSendCommandMutation();

  const handleDeleteCommand = async (record) => {
    try {
      const delCommand = [
        {
          command: record.command,
          edit: "delete",
          client: record.client,
        },
      ];
      await sendCommand(delCommand)?.unwrap();
      notification.success({
        message: "successfully deleted command!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  };

  const handleRerunCommand = async (record) => {
    try {
      const rerunCommand = [
        {
          command: record.command,
          client: record.client,
          kind: record.kind,
        },
      ];
      await sendCommand(rerunCommand)?.unwrap();
      notification.success({
        message: "successfully sent command to rerun!",
      });
    } catch (error) {
      notification.error({ message: error.data.error || error.data });
    }
  };

  const columns = [
    {
      title: "CMD",
      dataIndex: "command",
      key: "command",
      width: 580,
      render: (text) => (
        <Tooltip title={text} placement="topLeft">
          <Typography.Text
            ellipsis={{
              tooltip: false, // We're using custom Tooltip
            }}
            style={{
              maxWidth: "100%",
              cursor: "help",
            }}
          >
            {text}
          </Typography.Text>
        </Tooltip>
      ),
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      width: 150,
      align: "center",
    },
    {
      title: "Timestamp",
      dataIndex: "timestamp",
      key: "timestamp",
      defaultSortOrder: "descend",
      sorter: (a, b) => (a.timestamp > b.timestamp ? 1 : -1),
      render: (data) => {
        return dayjs(data).format("YYYY/MM/DD HH:mm:ss");
      },
      width: 200,
      align: "center",
    },
    {
      title: "Result",
      dataIndex: "result",
      key: "result",
      width: 400,
      align: "top",
      render: (data) => {
        return (
          <Typography.Paragraph
            ellipsis={{
              rows: 2,
              defaultExpanded: false,
              expandable: "collapsible",
            }}
            style={{ overflow: "auto", maxHeight: "250px" }}
          >
            {data}
          </Typography.Paragraph>
        );
      },
    },
    {
      title: "Service Name",
      dataIndex: "client",
      key: "client",
      render: (_, record) =>
        record.client !== "" ? record.client : record.name,
      width: 150,
      align: "center",
    },
    {
      title: "Index",
      dataIndex: "index",
      key: "index",
      width: 100,
      align: "center",
    },

    {
      title: "Action",
      dataIndex: "index",
      key: "action",
      width: 150,
      align: "center",
      fixed: "right",
      render: (_, record) => (
        <Space size={10}>
          <Tooltip title="delete cmd">
            <Button
              aria-label="delete cmd"
              danger
              icon={<DeleteOutlined />}
              size="small"
              onClick={() => handleDeleteCommand(record)}
            />
          </Tooltip>
          <Tooltip title="rerun cmd">
            <Button
              aria-label="rerun cmd"
              icon={<RetweetOutlined />}
              size="small"
              onClick={() => handleRerunCommand(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const recordAfterfiltering = (dataSource) => {
    return dataSource.filter((row) => {
      let rec = columns.map((element) => {
        return row[element?.dataIndex]
          ?.toString()
          ?.toLowerCase()
          ?.includes(inputSearch?.toLowerCase());
      });
      return rec.includes(true);
    });
  };

  return (
    <Modal
      open={open}
      width="100%"
      style={{ top: 20 }}
      forceRender
      maskClosable={false}
      onCancel={() => {
        onCancel();
      }}
      footer={null}
      destroyOnHidden
      afterOpenChange={() => refetch()}
    >
      <ProTable
        columns={columns}
        bordered
        rowKey="cmd_key"
        headerTitle="Command List"
        size="small"
        dataSource={recordAfterfiltering(cmdData || [])}
        pagination={{
          position: ["bottomCenter"],
          showQuickJumper: true,
          size: "default",
          total: recordAfterfiltering(cmdData || []).length,
          defaultPageSize: 10,
          pageSizeOptions: [10, 15, 20, 25],
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
        }}
        options={{
          reload: () => {
            refetch();
          },
          fullScreen: false,
          density: false,
          setting: false,
        }}
        toolbar={{
          search: {
            onSearch: (value) => {
              setInputSearch(value);
            },
          },
        }}
        search={false}
        scroll={{
          x: 1100,
        }}
        dateFormatter="string"
      />
    </Modal>
  );
};

export default CommandModal;
