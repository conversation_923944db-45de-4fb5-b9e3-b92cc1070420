import { describe, expect, it, vi, beforeEach, afterEach } from "vitest";
import { render, screen, cleanup } from "@testing-library/react";
import { Provider } from "react-redux";
import { store } from "../app/store";
import NetworkTopology from "../components/NetworkTopology";
import graphMemoryOptimizer from "../utils/graphMemoryOptimizer";

// Mock G6 to avoid actual graph rendering in tests
vi.mock("@antv/g6", () => ({
  default: {
    Graph: vi.fn().mockImplementation(() => ({
      destroy: vi.fn(),
      destroyed: false,
      off: vi.fn(),
      clear: vi.fn(),
      data: vi.fn(),
      render: vi.fn(),
      fitView: vi.fn(),
      changeSize: vi.fn(),
      on: vi.fn(),
      getNodes: vi.fn(() => []),
      getEdges: vi.fn(() => []),
      save: vi.fn(() => ({ nodes: [], edges: [] })),
      _memoryOptimizerName: "test-graph",
      _memoryOptimizerRegistered: Date.now(),
    })),
    registerNode: vi.fn(),
    registerEdge: vi.fn(),
    registerBehavior: vi.fn(),
    registerLayout: vi.fn(),
  },
}));

// Mock the API hooks
vi.mock("../app/services/groupsApi", () => ({
  useGetNetworkTopologyDataQuery: vi.fn(() => ({
    data: {
      topologyData: { nodes: [], edges: [] },
      networkData: [],
    },
    isLoading: false,
    error: null,
  })),
  useCleanupGroupsMutation: vi.fn(() => [
    vi
      .fn()
      .mockResolvedValue({
        unwrap: () => ({ hasChanges: false, message: "No changes" }),
      }),
    { isLoading: false },
  ]),
}));

// Mock graphMemoryOptimizer
vi.mock("../utils/graphMemoryOptimizer", () => ({
  default: {
    registerGraph: vi.fn(),
    unregisterGraph: vi.fn(),
  },
}));

describe("NetworkTopology Component", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock DOM methods
    Object.defineProperty(HTMLElement.prototype, "offsetWidth", {
      configurable: true,
      value: 800,
    });
    Object.defineProperty(HTMLElement.prototype, "offsetHeight", {
      configurable: true,
      value: 600,
    });
  });

  afterEach(() => {
    cleanup();
  });

  it("renders the NetworkTopology component", () => {
    render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    expect(screen.getByText("Network Topology")).toBeInTheDocument();
  });

  it("registers graph with memory optimizer on mount", () => {
    render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    // Check that registerGraph was called
    expect(graphMemoryOptimizer.registerGraph).toHaveBeenCalled();
  });

  it("handles graph cleanup properly on unmount", () => {
    const { unmount } = render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    // Unmount the component to trigger cleanup
    unmount();

    // Check that unregisterGraph was called during cleanup
    expect(graphMemoryOptimizer.unregisterGraph).toHaveBeenCalled();
  });

  it("handles graph destruction safely when graph is already destroyed", () => {
    // Create a mock graph that's already destroyed
    const mockDestroyedGraph = {
      destroy: vi.fn(),
      destroyed: true,
      off: vi.fn(),
      clear: vi.fn(),
      _memoryOptimizerName: "test-graph",
    };

    // Mock G6 to return the destroyed graph
    vi.doMock("@antv/g6", () => ({
      default: {
        Graph: vi.fn().mockImplementation(() => mockDestroyedGraph),
      },
    }));

    const { unmount } = render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    // Unmount should not throw errors even with destroyed graph
    expect(() => unmount()).not.toThrow();
  });

  it("handles missing graph methods gracefully", () => {
    // Create a mock graph with missing methods
    const mockIncompleteGraph = {
      destroyed: false,
      _memoryOptimizerName: "test-graph",
      // Missing destroy, off, clear methods
    };

    // Mock G6 to return the incomplete graph
    vi.doMock("@antv/g6", () => ({
      default: {
        Graph: vi.fn().mockImplementation(() => mockIncompleteGraph),
      },
    }));

    const { unmount } = render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    // Unmount should not throw errors even with missing methods
    expect(() => unmount()).not.toThrow();
  });

  it("displays loading state correctly", () => {
    // Mock loading state
    vi.doMock("../app/services/groupsApi", () => ({
      useGetNetworkTopologyDataQuery: vi.fn(() => ({
        data: null,
        isLoading: true,
        error: null,
      })),
      useCleanupGroupsMutation: vi.fn(() => [vi.fn(), { isLoading: false }]),
    }));

    render(
      <Provider store={store}>
        <NetworkTopology />
      </Provider>
    );

    expect(screen.getByTestId("loading-spinner")).toBeInTheDocument();
  });
});
